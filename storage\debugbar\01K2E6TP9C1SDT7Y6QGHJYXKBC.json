{"__meta": {"id": "01K2E6TP9C1SDT7Y6QGHJYXKBC", "datetime": "2025-08-12 10:57:48", "utime": **********.71743, "method": "POST", "uri": "/employer/change-info-company", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 10, "messages": [{"message": "[10:57:48] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.584165, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.584634, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-change-info-company' limit 1\\n-- \",\n    \"Time:\": 23.92\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.642147, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\",\\\"name\\\":\\\"C\\\\u00d4NG TY C\\\\u1ed4 PH\\\\u1ea6N ITNAVI VI\\\\u1ec6T NAM\\\",\\\"mst\\\":\\\"01079526431\\\",\\\"website\\\":\\\"https:\\\\\\/\\\\\\/itnavi.com.vn\\\\\\/\\\",\\\"scale\\\":\\\"51-100\\\",\\\"career\\\":[\\\"1\\\"],\\\"address\\\":[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\"},{\\\"area\\\":null,\\\"address\\\":null},{\\\"area\\\":null,\\\"address\\\":null}],\\\"about\\\":\\\"<p>ITNavi l&agrave; c&ocirc;ng ty c&ocirc;ng ngh\\\\u1ec7 cung c\\\\u1ea5p c&aacute;c gi\\\\u1ea3i ph&aacute;p tuy\\\\u1ec3n d\\\\u1ee5ng d&agrave;nh ri&ecirc;ng cho nh&acirc;n s\\\\u1ef1 m\\\\u1ea3ng IT. ITNavi \\\\u0111&atilde; v&agrave; \\\\u0111ang tr\\\\u1edf th&agrave;nh c\\\\u1ea7u n\\\\u1ed1i \\\\u0111&aacute;p \\\\u1ee9ng m\\\\u1ea1nh m\\\\u1ebd nhu c\\\\u1ea7u c\\\\u1ee7a Nh&agrave; tuy\\\\u1ec3n d\\\\u1ee5ng v&agrave; \\\\u1ee8ng vi&ecirc;n, \\\\u0111\\\\u1ed3ng th\\\\u1eddi ti&ecirc;n phong trong vi\\\\u1ec7c x&acirc;y d\\\\u1ef1ng \\\\u0111\\\\u1ecbnh h\\\\u01b0\\\\u1edbng s\\\\u1ef1 nghi\\\\u1ec7p cho \\\\u1ee9ng vi&ecirc;n. V\\\\u1edbi m\\\\u1ee5c ti&ecirc;u tr\\\\u1edf th&agrave;nh website h&agrave;ng \\\\u0111\\\\u1ea7u \\\\u0111\\\\u1ec3 k\\\\u1ebft n\\\\u1ed1i c&aacute;c t&agrave;i n\\\\u0103ng trong ng&agrave;nh c&ocirc;ng ngh\\\\u1ec7 v\\\\u1edbi doanh nghi\\\\u1ec7p, ITNavi hy v\\\\u1ecdng c&oacute; th\\\\u1ec3 \\\\u0111\\\\u1ed3ng h&agrave;nh c&ugrave;ng c\\\\u1ed9ng \\\\u0111\\\\u1ed3ng \\\\u0111\\\\u1ec3 t\\\\u1ea1o ra m\\\\u1ed9t s\\\\u1ea3n ph\\\\u1ea9m lan to\\\\u1ea3 r\\\\u1ed9ng r&atilde;i v&agrave; mang l\\\\u1ea1i gi&aacute; tr\\\\u1ecb cho c\\\\u1ed9ng \\\\u0111\\\\u1ed3ng. ITNavi l&agrave; t\\\\u1ed5 ch\\\\u1ee9c thu\\\\u1ed9c VTI Group v\\\\u1edbi quy m&ocirc; h\\\\u01a1n 500 nh&acirc;n s\\\\u1ef1. Ho\\\\u1ea1t \\\\u0111\\\\u1ed9ng t\\\\u1ea1i 3 chi nh&aacute;nh: H&agrave; N\\\\u1ed9i, H\\\\u1ed3 Ch&iacute; Minh v&agrave; Nh\\\\u1eadt B\\\\u1ea3n. Group VTI bao g\\\\u1ed3m c&aacute;c c&ocirc;ng ty:<br \\\\\\/>1. VTI Vi\\\\u1ec7t Nam: Ho\\\\u1ea1t \\\\u0111\\\\u1ed9ng trong l\\\\u0129nh v\\\\u1ef1c outsource<br \\\\\\/>2. VTI Japan: Ho\\\\u1ea1t \\\\u0111\\\\u1ed9ng l\\\\u0129nh v\\\\u1ef1c outsource<br \\\\\\/>3. VTI Academy: \\\\u0110&agrave;o t\\\\u1ea1o k\\\\u1ef9 s\\\\u01b0 IT<br \\\\\\/>4. VTI Cloud: Cung c\\\\u1ea5p gi\\\\u1ea3i ph&aacute;p AWS<br \\\\\\/>5. HRI: Cung c\\\\u1ea5p ngu\\\\u1ed3n l\\\\u1ef1c ng&agrave;nh IT<br \\\\\\/>6. Itnavi: Website tuy\\\\u1ec3n d\\\\u1ee5ng ng&agrave;nh IT<br \\\\\\/>Ngo&agrave;i cung c\\\\u1ea5p d\\\\u1ecbch v\\\\u1ee5 cho c&aacute;c c&ocirc;ng ty IT b&ecirc;n ngo&agrave;i, ch&uacute;ng t&ocirc;i \\\\u0111&oacute;ng vai tr&ograve; quan tr\\\\u1ecdng trong vi\\\\u1ec7c build-up h\\\\u1ec7 th\\\\u1ed1ng nh&acirc;n s\\\\u1ef1 cho c&ocirc;ng ty VTI trong c&ugrave;ng Group<\\\\\\/p>\\\",\\\"video\\\":[null]}', 'http:\\/\\/recland.local\\/employer\\/change-info-company', 'http:\\/\\/recland.local\\/employer\\/company-profile', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"3850\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\\\\\/form-data; boundary=----WebKitFormBoundaryXwiB2ruAX3791WXK\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/company-profile\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InFENk5VYi9XaGFLY2lFWkNVb0VST2c9PSIsInZhbHVlIjoicDhMajZtNXlYcVVMYklTTWpjbWl2Y0x0N1l3eUNVc2NrWUNQVWVQcFc4VGoycks2eGtTRlROM05ydEsyWis5UVRjU1lWWWppV2RTTXNjbDUzU1B1VnU1R244OWpMbm4zSE9IZmlvZmJ1M2Y1ZEFWbWJHK204TnNiRHU3eExOT0YiLCJtYWMiOiJjNDhkMmM2ZTg5ODhkMDA0Yjk5ZWI5ZWIwYjMxODMyOTE5OTU4ZWQzYmVkYzIwYWI0YzYxZjRmYTU5YjA0YjRkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjN2c1hZRFFnVjVvbTdjeFRXVXJWYVE9PSIsInZhbHVlIjoiVGdIV0FubkhWeUtnRUpGbkhaNm1ET2JQYjhuNktEdnZ0N2QxMVpVZ2lxalZRVzh6eCtMUlJadmZhVlJMVmF1YXFuT0F4dlUrajk4eDVyTG96bHBDeHNFcG9XQ3RUNkZKajd3MGVteXgrRW5zdWhEY3hVcmh0Q2xtUkRiVy93d0ciLCJtYWMiOiIzODkwNWZjZTAzODIyMWZmNmViMjU4OWRjMGY2ODE1NjlkYmNlNjZjM2VkYTVlZjNmYWM5NWM1OTk3N2QwMDMxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754971051$o3$g1$t**********$j43$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 10:57:48', '2025-08-12 10:57:48')\\n-- \",\n    \"Time:\": 0.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.683235, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.61\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.688359, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.83\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.69573, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.33\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.698064, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 4.13\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.703849, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' limit 1\\n-- \",\n    \"Time:\": 0.5\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.706448, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:48] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nupdate `companies` set `address` = '[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\\\u00e0 AC, ng\\\\u00f5 78 Duy T\\\\u00e2n - C\\\\u1ea7u Gi\\\\u1ea5y - H\\\\u00e0 N\\\\u1ed9i\\\"},{\\\"area\\\":null,\\\"address\\\":null},{\\\"area\\\":null,\\\"address\\\":null}]', `about` = '<p>ITNavi l&agrave; c&ocirc;ng ty c&ocirc;ng ngh\\u1ec7 cung c\\u1ea5p c&aacute;c gi\\u1ea3i ph&aacute;p tuy\\u1ec3n d\\u1ee5ng d&agrave;nh ri&ecirc;ng cho nh&acirc;n s\\u1ef1 m\\u1ea3ng IT. ITNavi \\u0111&atilde; v&agrave; \\u0111ang tr\\u1edf th&agrave;nh c\\u1ea7u n\\u1ed1i \\u0111&aacute;p \\u1ee9ng m\\u1ea1nh m\\u1ebd nhu c\\u1ea7u c\\u1ee7a Nh&agrave; tuy\\u1ec3n d\\u1ee5ng v&agrave; \\u1ee8ng vi&ecirc;n, \\u0111\\u1ed3ng th\\u1eddi ti&ecirc;n phong trong vi\\u1ec7c x&acirc;y d\\u1ef1ng \\u0111\\u1ecbnh h\\u01b0\\u1edbng s\\u1ef1 nghi\\u1ec7p cho \\u1ee9ng vi&ecirc;n. V\\u1edbi m\\u1ee5c ti&ecirc;u tr\\u1edf th&agrave;nh website h&agrave;ng \\u0111\\u1ea7u \\u0111\\u1ec3 k\\u1ebft n\\u1ed1i c&aacute;c t&agrave;i n\\u0103ng trong ng&agrave;nh c&ocirc;ng ngh\\u1ec7 v\\u1edbi doanh nghi\\u1ec7p, ITNavi hy v\\u1ecdng c&oacute; th\\u1ec3 \\u0111\\u1ed3ng h&agrave;nh c&ugrave;ng c\\u1ed9ng \\u0111\\u1ed3ng \\u0111\\u1ec3 t\\u1ea1o ra m\\u1ed9t s\\u1ea3n ph\\u1ea9m lan to\\u1ea3 r\\u1ed9ng r&atilde;i v&agrave; mang l\\u1ea1i gi&aacute; tr\\u1ecb cho c\\u1ed9ng \\u0111\\u1ed3ng. ITNavi l&agrave; t\\u1ed5 ch\\u1ee9c thu\\u1ed9c VTI Group v\\u1edbi quy m&ocirc; h\\u01a1n 500 nh&acirc;n s\\u1ef1. Ho\\u1ea1t \\u0111\\u1ed9ng t\\u1ea1i 3 chi nh&aacute;nh: H&agrave; N\\u1ed9i, H\\u1ed3 Ch&iacute; Minh v&agrave; Nh\\u1eadt B\\u1ea3n. Group VTI bao g\\u1ed3m c&aacute;c c&ocirc;ng ty:<br \\/>1. VTI Vi\\u1ec7t Nam: Ho\\u1ea1t \\u0111\\u1ed9ng trong l\\u0129nh v\\u1ef1c outsource<br \\/>2. VTI Japan: Ho\\u1ea1t \\u0111\\u1ed9ng l\\u0129nh v\\u1ef1c outsource<br \\/>3. VTI Academy: \\u0110&agrave;o t\\u1ea1o k\\u1ef9 s\\u01b0 IT<br \\/>4. VTI Cloud: Cung c\\u1ea5p gi\\u1ea3i ph&aacute;p AWS<br \\/>5. HRI: Cung c\\u1ea5p ngu\\u1ed3n l\\u1ef1c ng&agrave;nh IT<br \\/>6. Itnavi: Website tuy\\u1ec3n d\\u1ee5ng ng&agrave;nh IT<br \\/>Ngo&agrave;i cung c\\u1ea5p d\\u1ecbch v\\u1ee5 cho c&aacute;c c&ocirc;ng ty IT b&ecirc;n ngo&agrave;i, ch&uacute;ng t&ocirc;i \\u0111&oacute;ng vai tr&ograve; quan tr\\u1ecdng trong vi\\u1ec7c build-up h\\u1ec7 th\\u1ed1ng nh&acirc;n s\\u1ef1 cho c&ocirc;ng ty VTI trong c&ugrave;ng Group<\\/p>', `image` = '\\\"[]\\\"', `companies`.`updated_at` = '2025-08-12 10:57:48' where `id` = '84'\\n-- \",\n    \"Time:\": 0.65\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.708975, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.14094, "end": **********.717459, "duration": 0.5765190124511719, "duration_str": "577ms", "measures": [{"label": "Booting", "start": **********.14094, "relative_start": 0, "end": **********.560865, "relative_end": **********.560865, "duration": 0.****************, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.560875, "relative_start": 0.****************, "end": **********.717462, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.574446, "relative_start": 0.****************, "end": **********.579875, "relative_end": **********.579875, "duration": 0.*****************, "duration_str": "5.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST employer/change-info-company", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@changeInfoCompany<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=923\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-change-info-company", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=923\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:923-931</a>"}, "queries": {"count": 8, "nb_statements": 8, "nb_visible_statements": 8, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03174, "accumulated_duration_str": "31.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-change-info-company' limit 1", "type": "query", "params": [], "bindings": ["employer-change-info-company"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.6185238, "duration": 0.02392, "duration_str": "23.92ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "b25d3b78ee33626719ed83b5ee0026ebc81cc447c8b8c2c5a0c58d9fad84eed5"}, "start_percent": 0, "width_percent": 75.362}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('POST', '{\\\"_token\\\":\\\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\\\",\\\"name\\\":\\\"C\\u00d4NG TY C\\u1ed4 PH\\u1ea6N ITNAVI VI\\u1ec6T NAM\\\",\\\"mst\\\":\\\"01079526431\\\",\\\"website\\\":\\\"https:\\/\\/itnavi.com.vn\\/\\\",\\\"scale\\\":\\\"51-100\\\",\\\"career\\\":[\\\"1\\\"],\\\"address\\\":[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\\\"},{\\\"area\\\":null,\\\"address\\\":null},{\\\"area\\\":null,\\\"address\\\":null}],\\\"about\\\":\\\"<p>ITNavi l&agrave; c&ocirc;ng ty c&ocirc;ng ngh\\u1ec7 cung c\\u1ea5p c&aacute;c gi\\u1ea3i ph&aacute;p tuy\\u1ec3n d\\u1ee5ng d&agrave;nh ri&ecirc;ng cho nh&acirc;n s\\u1ef1 m\\u1ea3ng IT. ITNavi \\u0111&atilde; v&agrave; \\u0111ang tr\\u1edf th&agrave;nh c\\u1ea7u n\\u1ed1i \\u0111&aacute;p \\u1ee9ng m\\u1ea1nh m\\u1ebd nhu c\\u1ea7u c\\u1ee7a Nh&agrave; tuy\\u1ec3n d\\u1ee5ng v&agrave; \\u1ee8ng vi&ecirc;n, \\u0111\\u1ed3ng th\\u1eddi ti&ecirc;n phong trong vi\\u1ec7c x&acirc;y d\\u1ef1ng \\u0111\\u1ecbnh h\\u01b0\\u1edbng s\\u1ef1 nghi\\u1ec7p cho \\u1ee9ng vi&ecirc;n. V\\u1edbi m\\u1ee5c ti&ecirc;u tr\\u1edf th&agrave;nh website h&agrave;ng \\u0111\\u1ea7u \\u0111\\u1ec3 k\\u1ebft n\\u1ed1i c&aacute;c t&agrave;i n\\u0103ng trong ng&agrave;nh c&ocirc;ng ngh\\u1ec7 v\\u1edbi doanh nghi\\u1ec7p, ITNavi hy v\\u1ecdng c&oacute; th\\u1ec3 \\u0111\\u1ed3ng h&agrave;nh c&ugrave;ng c\\u1ed9ng \\u0111\\u1ed3ng \\u0111\\u1ec3 t\\u1ea1o ra m\\u1ed9t s\\u1ea3n ph\\u1ea9m lan to\\u1ea3 r\\u1ed9ng r&atilde;i v&agrave; mang l\\u1ea1i gi&aacute; tr\\u1ecb cho c\\u1ed9ng \\u0111\\u1ed3ng. ITNavi l&agrave; t\\u1ed5 ch\\u1ee9c thu\\u1ed9c VTI Group v\\u1edbi quy m&ocirc; h\\u01a1n 500 nh&acirc;n s\\u1ef1. Ho\\u1ea1t \\u0111\\u1ed9ng t\\u1ea1i 3 chi nh&aacute;nh: H&agrave; N\\u1ed9i, H\\u1ed3 Ch&iacute; Minh v&agrave; Nh\\u1eadt B\\u1ea3n. Group VTI bao g\\u1ed3m c&aacute;c c&ocirc;ng ty:<br \\/>1. VTI Vi\\u1ec7t Nam: Ho\\u1ea1t \\u0111\\u1ed9ng trong l\\u0129nh v\\u1ef1c outsource<br \\/>2. VTI Japan: Ho\\u1ea1t \\u0111\\u1ed9ng l\\u0129nh v\\u1ef1c outsource<br \\/>3. VTI Academy: \\u0110&agrave;o t\\u1ea1o k\\u1ef9 s\\u01b0 IT<br \\/>4. VTI Cloud: Cung c\\u1ea5p gi\\u1ea3i ph&aacute;p AWS<br \\/>5. HRI: Cung c\\u1ea5p ngu\\u1ed3n l\\u1ef1c ng&agrave;nh IT<br \\/>6. Itnavi: Website tuy\\u1ec3n d\\u1ee5ng ng&agrave;nh IT<br \\/>Ngo&agrave;i cung c\\u1ea5p d\\u1ecbch v\\u1ee5 cho c&aacute;c c&ocirc;ng ty IT b&ecirc;n ngo&agrave;i, ch&uacute;ng t&ocirc;i \\u0111&oacute;ng vai tr&ograve; quan tr\\u1ecdng trong vi\\u1ec7c build-up h\\u1ec7 th\\u1ed1ng nh&acirc;n s\\u1ef1 cho c&ocirc;ng ty VTI trong c&ugrave;ng Group<\\/p>\\\",\\\"video\\\":[null]}', 'http://recland.local/employer/change-info-company', 'http://recland.local/employer/company-profile', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"content-length\\\":[\\\"3850\\\"],\\\"cache-control\\\":[\\\"max-age=0\\\"],\\\"origin\\\":[\\\"http:\\/\\/recland.local\\\"],\\\"content-type\\\":[\\\"multipart\\/form-data; boundary=----WebKitFormBoundaryXwiB2ruAX3791WXK\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/company-profile\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InFENk5VYi9XaGFLY2lFWkNVb0VST2c9PSIsInZhbHVlIjoicDhMajZtNXlYcVVMYklTTWpjbWl2Y0x0N1l3eUNVc2NrWUNQVWVQcFc4VGoycks2eGtTRlROM05ydEsyWis5UVRjU1lWWWppV2RTTXNjbDUzU1B1VnU1R244OWpMbm4zSE9IZmlvZmJ1M2Y1ZEFWbWJHK204TnNiRHU3eExOT0YiLCJtYWMiOiJjNDhkMmM2ZTg5ODhkMDA0Yjk5ZWI5ZWIwYjMxODMyOTE5OTU4ZWQzYmVkYzIwYWI0YzYxZjRmYTU5YjA0YjRkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjN2c1hZRFFnVjVvbTdjeFRXVXJWYVE9PSIsInZhbHVlIjoiVGdIV0FubkhWeUtnRUpGbkhaNm1ET2JQYjhuNktEdnZ0N2QxMVpVZ2lxalZRVzh6eCtMUlJadmZhVlJMVmF1YXFuT0F4dlUrajk4eDVyTG96bHBDeHNFcG9XQ3RUNkZKajd3MGVteXgrRW5zdWhEY3hVcmh0Q2xtUkRiVy93d0ciLCJtYWMiOiIzODkwNWZjZTAzODIyMWZmNmViMjU4OWRjMGY2ODE1NjlkYmNlNjZjM2VkYTVlZjNmYWM5NWM1OTk3N2QwMDMxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754971051$o3$g1$t**********$j43$l0$h0\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 10:57:48', '2025-08-12 10:57:48')", "type": "query", "params": [], "bindings": ["POST", "{\"_token\":\"hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna\",\"name\":\"C\\u00d4NG TY C\\u1ed4 PH\\u1ea6N ITNAVI VI\\u1ec6T NAM\",\"mst\":\"01079526431\",\"website\":\"https:\\/\\/itnavi.com.vn\\/\",\"scale\":\"51-100\",\"career\":[\"1\"],\"address\":[{\"area\":\"ha-noi\",\"address\":\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\"},{\"area\":null,\"address\":null},{\"area\":null,\"address\":null}],\"about\":\"<p>ITNavi l&agrave; c&ocirc;ng ty c&ocirc;ng ngh\\u1ec7 cung c\\u1ea5p c&aacute;c gi\\u1ea3i ph&aacute;p tuy\\u1ec3n d\\u1ee5ng d&agrave;nh ri&ecirc;ng cho nh&acirc;n s\\u1ef1 m\\u1ea3ng IT. ITNavi \\u0111&atilde; v&agrave; \\u0111ang tr\\u1edf th&agrave;nh c\\u1ea7u n\\u1ed1i \\u0111&aacute;p \\u1ee9ng m\\u1ea1nh m\\u1ebd nhu c\\u1ea7u c\\u1ee7a Nh&agrave; tuy\\u1ec3n d\\u1ee5ng v&agrave; \\u1ee8ng vi&ecirc;n, \\u0111\\u1ed3ng th\\u1eddi ti&ecirc;n phong trong vi\\u1ec7c x&acirc;y d\\u1ef1ng \\u0111\\u1ecbnh h\\u01b0\\u1edbng s\\u1ef1 nghi\\u1ec7p cho \\u1ee9ng vi&ecirc;n. V\\u1edbi m\\u1ee5c ti&ecirc;u tr\\u1edf th&agrave;nh website h&agrave;ng \\u0111\\u1ea7u \\u0111\\u1ec3 k\\u1ebft n\\u1ed1i c&aacute;c t&agrave;i n\\u0103ng trong ng&agrave;nh c&ocirc;ng ngh\\u1ec7 v\\u1edbi doanh nghi\\u1ec7p, ITNavi hy v\\u1ecdng c&oacute; th\\u1ec3 \\u0111\\u1ed3ng h&agrave;nh c&ugrave;ng c\\u1ed9ng \\u0111\\u1ed3ng \\u0111\\u1ec3 t\\u1ea1o ra m\\u1ed9t s\\u1ea3n ph\\u1ea9m lan to\\u1ea3 r\\u1ed9ng r&atilde;i v&agrave; mang l\\u1ea1i gi&aacute; tr\\u1ecb cho c\\u1ed9ng \\u0111\\u1ed3ng. ITNavi l&agrave; t\\u1ed5 ch\\u1ee9c thu\\u1ed9c VTI Group v\\u1edbi quy m&ocirc; h\\u01a1n 500 nh&acirc;n s\\u1ef1. Ho\\u1ea1t \\u0111\\u1ed9ng t\\u1ea1i 3 chi nh&aacute;nh: H&agrave; N\\u1ed9i, H\\u1ed3 Ch&iacute; Minh v&agrave; Nh\\u1eadt B\\u1ea3n. Group VTI bao g\\u1ed3m c&aacute;c c&ocirc;ng ty:<br \\/>1. VTI Vi\\u1ec7t Nam: Ho\\u1ea1t \\u0111\\u1ed9ng trong l\\u0129nh v\\u1ef1c outsource<br \\/>2. VTI Japan: Ho\\u1ea1t \\u0111\\u1ed9ng l\\u0129nh v\\u1ef1c outsource<br \\/>3. VTI Academy: \\u0110&agrave;o t\\u1ea1o k\\u1ef9 s\\u01b0 IT<br \\/>4. VTI Cloud: Cung c\\u1ea5p gi\\u1ea3i ph&aacute;p AWS<br \\/>5. HRI: Cung c\\u1ea5p ngu\\u1ed3n l\\u1ef1c ng&agrave;nh IT<br \\/>6. Itnavi: Website tuy\\u1ec3n d\\u1ee5ng ng&agrave;nh IT<br \\/>Ngo&agrave;i cung c\\u1ea5p d\\u1ecbch v\\u1ee5 cho c&aacute;c c&ocirc;ng ty IT b&ecirc;n ngo&agrave;i, ch&uacute;ng t&ocirc;i \\u0111&oacute;ng vai tr&ograve; quan tr\\u1ecdng trong vi\\u1ec7c build-up h\\u1ec7 th\\u1ed1ng nh&acirc;n s\\u1ef1 cho c&ocirc;ng ty VTI trong c&ugrave;ng Group<\\/p>\",\"video\":[null]}", "http://recland.local/employer/change-info-company", "http://recland.local/employer/company-profile", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"content-length\":[\"3850\"],\"cache-control\":[\"max-age=0\"],\"origin\":[\"http:\\/\\/recland.local\"],\"content-type\":[\"multipart\\/form-data; boundary=----WebKitFormBoundaryXwiB2ruAX3791WXK\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/company-profile\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InFENk5VYi9XaGFLY2lFWkNVb0VST2c9PSIsInZhbHVlIjoicDhMajZtNXlYcVVMYklTTWpjbWl2Y0x0N1l3eUNVc2NrWUNQVWVQcFc4VGoycks2eGtTRlROM05ydEsyWis5UVRjU1lWWWppV2RTTXNjbDUzU1B1VnU1R244OWpMbm4zSE9IZmlvZmJ1M2Y1ZEFWbWJHK204TnNiRHU3eExOT0YiLCJtYWMiOiJjNDhkMmM2ZTg5ODhkMDA0Yjk5ZWI5ZWIwYjMxODMyOTE5OTU4ZWQzYmVkYzIwYWI0YzYxZjRmYTU5YjA0YjRkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjN2c1hZRFFnVjVvbTdjeFRXVXJWYVE9PSIsInZhbHVlIjoiVGdIV0FubkhWeUtnRUpGbkhaNm1ET2JQYjhuNktEdnZ0N2QxMVpVZ2lxalZRVzh6eCtMUlJadmZhVlJMVmF1YXFuT0F4dlUrajk4eDVyTG96bHBDeHNFcG9XQ3RUNkZKajd3MGVteXgrRW5zdWhEY3hVcmh0Q2xtUkRiVy93d0ciLCJtYWMiOiIzODkwNWZjZTAzODIyMWZmNmViMjU4OWRjMGY2ODE1NjlkYmNlNjZjM2VkYTVlZjNmYWM5NWM1OTk3N2QwMDMxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754971051$o3$g1$t**********$j43$l0$h0\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 10:57:48", "2025-08-12 10:57:48"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.6825378, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 75.362, "width_percent": 2.426}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.687821, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 77.788, "width_percent": 1.922}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.694973, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 79.71, "width_percent": 2.615}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.697805, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 82.325, "width_percent": 1.04}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 1723 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.69979, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ef913f8afc9f4d48f551727e02040c91d14047cbc5f6bb1c2d8490bb794fe689"}, "start_percent": 83.365, "width_percent": 13.012}, {"sql": "select * from `companies` where `companies`.`id` = 84 limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, {"index": 17, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 139}, {"index": 18, "namespace": null, "name": "app/Services/Frontend/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\CompanyService.php", "line": 90}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 926}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.706018, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:118", "source": {"index": 16, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=118", "ajax": false, "filename": "BaseRepository.php", "line": "118"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? limit 1", "hash": "cc1297ee2bc563ebfe1a5606d32a0266e1723b55aa2725a781741e51d0fb88ab"}, "start_percent": 96.377, "width_percent": 1.575}, {"sql": "update `companies` set `address` = '[{\\\"area\\\":\\\"ha-noi\\\",\\\"address\\\":\\\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\\\"},{\\\"area\\\":null,\\\"address\\\":null},{\\\"area\\\":null,\\\"address\\\":null}]', `about` = '<p>ITNavi l&agrave; c&ocirc;ng ty c&ocirc;ng nghệ cung cấp c&aacute;c giải ph&aacute;p tuyển dụng d&agrave;nh ri&ecirc;ng cho nh&acirc;n sự mảng IT. ITNavi đ&atilde; v&agrave; đang trở th&agrave;nh cầu nối đ&aacute;p ứng mạnh mẽ nhu cầu của Nh&agrave; tuyển dụng v&agrave; Ứng vi&ecirc;n, đồng thời ti&ecirc;n phong trong việc x&acirc;y dựng định hướng sự nghiệp cho ứng vi&ecirc;n. Với mục ti&ecirc;u trở th&agrave;nh website h&agrave;ng đầu để kết nối c&aacute;c t&agrave;i năng trong ng&agrave;nh c&ocirc;ng nghệ với doanh nghiệp, ITNavi hy vọng c&oacute; thể đồng h&agrave;nh c&ugrave;ng cộng đồng để tạo ra một sản phẩm lan toả rộng r&atilde;i v&agrave; mang lại gi&aacute; trị cho cộng đồng. ITNavi l&agrave; tổ chức thuộc VTI Group với quy m&ocirc; hơn 500 nh&acirc;n sự. Hoạt động tại 3 chi nh&aacute;nh: H&agrave; Nội, Hồ Ch&iacute; Minh v&agrave; Nhật Bản. Group VTI bao gồm c&aacute;c c&ocirc;ng ty:<br />1. VTI Việt Nam: Hoạt động trong lĩnh vực outsource<br />2. VTI Japan: Hoạt động lĩnh vực outsource<br />3. VTI Academy: Đ&agrave;o tạo kỹ sư IT<br />4. VTI Cloud: Cung cấp giải ph&aacute;p AWS<br />5. HRI: Cung cấp nguồn lực ng&agrave;nh IT<br />6. Itnavi: Website tuyển dụng ng&agrave;nh IT<br />Ngo&agrave;i cung cấp dịch vụ cho c&aacute;c c&ocirc;ng ty IT b&ecirc;n ngo&agrave;i, ch&uacute;ng t&ocirc;i đ&oacute;ng vai tr&ograve; quan trọng trong việc build-up hệ thống nh&acirc;n sự cho c&ocirc;ng ty VTI trong c&ugrave;ng Group</p>', `image` = '\\\"[]\\\"', `companies`.`updated_at` = '2025-08-12 10:57:48' where `id` = 84", "type": "query", "params": [], "bindings": ["[{\"area\":\"ha-noi\",\"address\":\"To\\u00e0 AC, ng\\u00f5 78 Duy T\\u00e2n - C\\u1ea7u Gi\\u1ea5y - H\\u00e0 N\\u1ed9i\"},{\"area\":null,\"address\":null},{\"area\":null,\"address\":null}]", "<p>ITNavi l&agrave; c&ocirc;ng ty c&ocirc;ng nghệ cung cấp c&aacute;c giải ph&aacute;p tuyển dụng d&agrave;nh ri&ecirc;ng cho nh&acirc;n sự mảng IT. ITNavi đ&atilde; v&agrave; đang trở th&agrave;nh cầu nối đ&aacute;p <PERSON>ng mạnh mẽ nhu cầu của Nh&agrave; tuyển dụng v&agrave; Ứng vi&ecirc;n, đồng thời ti&ecirc;n phong trong việc x&acirc;y dựng định hướng sự nghiệp cho ứng vi&ecirc;n. Với mục ti&ecirc;u trở th&agrave;nh website h&agrave;ng đầu để kết nối c&aacute;c t&agrave;i năng trong ng&agrave;nh c&ocirc;ng nghệ với do<PERSON>h nghi<PERSON>, ITNavi hy vọng c&oacute; thể đồng h&agrave;nh c&ugrave;ng cộng đồng để tạo ra một sản phẩm lan toả rộng r&atilde;i v&agrave; mang lại gi&aacute; trị cho cộng đồng. ITNavi l&agrave; tổ chức thuộc VTI Group với quy m&ocirc; hơn 500 nh&acirc;n sự. Hoạt động tại 3 chi nh&aacute;nh: H&agrave; Nội, Hồ Ch&iacute; Minh v&agrave; Nhật Bản. Group VTI bao gồm c&aacute;c c&ocirc;ng ty:<br />1. VTI Việt Nam: Hoạt động trong lĩnh vực outsource<br />2. VTI Japan: Hoạt động lĩnh vực outsource<br />3. VTI Academy: Đ&agrave;o tạo kỹ sư IT<br />4. VTI Cloud: Cung cấp giải ph&aacute;p AWS<br />5. HRI: Cung cấp nguồn lực ng&agrave;nh IT<br />6. Itnavi: Website tuyển dụng ng&agrave;nh IT<br />Ngo&agrave;i cung cấp dịch vụ cho c&aacute;c c&ocirc;ng ty IT b&ecirc;n ngo&agrave;i, ch&uacute;ng t&ocirc;i đ&oacute;ng vai tr&ograve; quan trọng trong việc build-up hệ thống nh&acirc;n sự cho c&ocirc;ng ty VTI trong c&ugrave;ng Group</p>", "\"[]\"", "2025-08-12 10:57:48", 84], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, {"index": 15, "namespace": null, "name": "app/Services/Frontend/CompanyService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\CompanyService.php", "line": 90}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 926}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.708396, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:143", "source": {"index": 14, "namespace": null, "name": "app/Repositories/BaseRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FBaseRepository.php&line=143", "ajax": false, "filename": "BaseRepository.php", "line": "143"}, "connection": "hri_recland_product", "explain": null, "start_percent": 97.952, "width_percent": 2.048}]}, "models": {"data": {"App\\Models\\Company": {"retrieved": 1, "updated": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\EmployerType": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}}, "count": 6, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"created": 1, "retrieved": 4, "updated": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/company-profile\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:1 [\n    0 => \"toastr::messages\"\n  ]\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "PHPDEBUGBAR_STACK_DATA": "[]", "toastr::messages": "array:1 [\n  0 => array:4 [\n    \"type\" => \"success\"\n    \"title\" => \"Success\"\n    \"message\" => \"<PERSON><PERSON> lưu thành công bản ghi\"\n    \"options\" => []\n  ]\n]"}, "request": {"data": {"status": "302 Found", "full_url": "http://recland.local/employer/change-info-company", "action_name": "employer-change-info-company", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@changeInfoCompany", "uri": "POST employer/change-info-company", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@changeInfoCompany<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=923\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=923\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:923-931</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "576ms", "peak_memory": "44MB", "response": "Redirect to http://recland.local/employer/company-profile", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1595212825 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C&#212;NG TY C&#7892; PH&#7846;N ITNAVI VI&#7878;T NAM</span>\"\n  \"<span class=sf-dump-key>mst</span>\" => \"<span class=sf-dump-str title=\"11 characters\">01079526431</span>\"\n  \"<span class=sf-dump-key>website</span>\" => \"<span class=sf-dump-str title=\"22 characters\">https://itnavi.com.vn/</span>\"\n  \"<span class=sf-dump-key>scale</span>\" => \"<span class=sf-dump-str title=\"6 characters\">51-100</span>\"\n  \"<span class=sf-dump-key>career</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>area</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ha-noi</span>\"\n      \"<span class=sf-dump-key>address</span>\" => \"<span class=sf-dump-str title=\"42 characters\">To&#224; AC, ng&#245; 78 Duy T&#226;n - C&#7847;u Gi&#7845;y - H&#224; N&#7897;i</span>\"\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>area</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>area</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>about</span>\" => \"<span class=sf-dump-str title=\"1453 characters\">&lt;p&gt;ITNavi l&amp;agrave; c&amp;ocirc;ng ty c&amp;ocirc;ng ngh&#7879; cung c&#7845;p c&amp;aacute;c gi&#7843;i ph&amp;aacute;p tuy&#7875;n d&#7909;ng d&amp;agrave;nh ri&amp;ecirc;ng cho nh&amp;acirc;n s&#7921; m&#7843;ng IT. ITNavi &#273;&amp;atilde; v&amp;agrave; &#273;ang tr&#7903; th&amp;agrave;nh c&#7847;u n&#7889;i &#273;&amp;aacute;p &#7913;ng m&#7841;nh m&#7869; nhu c&#7847;u c&#7911;a Nh&amp;agrave; tuy&#7875;n d&#7909;ng v&amp;agrave; &#7912;ng vi&amp;ecirc;n, &#273;&#7891;ng th&#7901;i ti&amp;ecirc;n phong trong vi&#7879;c x&amp;acirc;y d&#7921;ng &#273;&#7883;nh h&#432;&#7899;ng s&#7921; nghi&#7879;p cho &#7913;ng vi&amp;ecirc;n. V&#7899;i m&#7909;c ti&amp;ecirc;u tr&#7903; th&amp;agrave;nh website h&amp;agrave;ng &#273;&#7847;u &#273;&#7875; k&#7871;t n&#7889;i c&amp;aacute;c t&amp;agrave;i n&#259;ng trong ng&amp;agrave;nh c&amp;ocirc;ng ngh&#7879; v&#7899;i doanh nghi&#7879;p, ITNavi hy v&#7885;ng c&amp;oacute; th&#7875; &#273;&#7891;ng h&amp;agrave;nh c&amp;ugrave;ng c&#7897;ng &#273;&#7891;ng &#273;&#7875; t&#7841;o ra m&#7897;t s&#7843;n ph&#7849;m lan to&#7843; r&#7897;ng r&amp;atilde;i v&amp;agrave; mang l&#7841;i gi&amp;aacute; tr&#7883; cho c&#7897;ng &#273;&#7891;ng. ITNavi l&amp;agrave; t&#7893; ch&#7913;c thu&#7897;c VTI Group v&#7899;i quy m&amp;ocirc; h&#417;n 500 nh&amp;acirc;n s&#7921;. Ho&#7841;t &#273;&#7897;ng t&#7841;i 3 chi nh&amp;aacute;nh: H&amp;agrave; N&#7897;i, H&#7891; Ch&amp;iacute; Minh v&amp;agrave; Nh&#7853;t B&#7843;n. Group VTI bao g&#7891;m c&amp;aacute;c c&amp;ocirc;ng ty:&lt;br /&gt;1. VTI Vi&#7879;t Nam: Ho&#7841;t &#273;&#7897;ng trong l&#297;nh v&#7921;c outsource&lt;br /&gt;2. VTI Japan: Ho&#7841;t &#273;&#7897;ng l&#297;nh v&#7921;c outsource&lt;br /&gt;3. VTI Academy: &#272;&amp;agrave;o t&#7841;o k&#7929; s&#432; IT&lt;br /&gt;4. VTI Cloud: Cung c&#7845;p gi&#7843;i ph&amp;aacute;p AWS&lt;br /&gt;5. HRI: Cung c&#7845;p ngu&#7891;n l&#7921;c ng&amp;agrave;nh IT&lt;br /&gt;6. Itnavi: Website tuy&#7875;n d&#7909;ng ng&amp;agrave;nh IT&lt;br /&gt;Ngo&amp;agrave;i cung c&#7845;p d&#7883;ch v&#7909; cho c&amp;aacute;c c&amp;ocirc;ng ty IT b&amp;ecirc;n ngo&amp;agrave;i, ch&amp;uacute;ng t&amp;ocirc;i &#273;&amp;oacute;ng vai tr&amp;ograve; quan tr&#7885;ng trong vi&#7879;c build-up h&#7879; th&#7889;ng nh&amp;acirc;n s&#7921; cho c&amp;ocirc;ng ty VTI trong c&amp;ugrave;ng Group&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>video</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595212825\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-590337764 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3850</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryXwiB2ruAX3791WXK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://recland.local/employer/company-profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; XSRF-TOKEN=eyJpdiI6InFENk5VYi9XaGFLY2lFWkNVb0VST2c9PSIsInZhbHVlIjoicDhMajZtNXlYcVVMYklTTWpjbWl2Y0x0N1l3eUNVc2NrWUNQVWVQcFc4VGoycks2eGtTRlROM05ydEsyWis5UVRjU1lWWWppV2RTTXNjbDUzU1B1VnU1R244OWpMbm4zSE9IZmlvZmJ1M2Y1ZEFWbWJHK204TnNiRHU3eExOT0YiLCJtYWMiOiJjNDhkMmM2ZTg5ODhkMDA0Yjk5ZWI5ZWIwYjMxODMyOTE5OTU4ZWQzYmVkYzIwYWI0YzYxZjRmYTU5YjA0YjRkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjN2c1hZRFFnVjVvbTdjeFRXVXJWYVE9PSIsInZhbHVlIjoiVGdIV0FubkhWeUtnRUpGbkhaNm1ET2JQYjhuNktEdnZ0N2QxMVpVZ2lxalZRVzh6eCtMUlJadmZhVlJMVmF1YXFuT0F4dlUrajk4eDVyTG96bHBDeHNFcG9XQ3RUNkZKajd3MGVteXgrRW5zdWhEY3hVcmh0Q2xtUkRiVy93d0ciLCJtYWMiOiIzODkwNWZjZTAzODIyMWZmNmViMjU4OWRjMGY2ODE1NjlkYmNlNjZjM2VkYTVlZjNmYWM5NWM1OTk3N2QwMDMxIiwidGFnIjoiIn0%3D; _ga_G4VPZYJ5H7=GS2.1.s1754971051$o3$g1$t**********$j43$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590337764\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1406430633 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1406430633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-97191965 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 03:57:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://recland.local/employer/company-profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-97191965\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1779616327 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://recland.local/employer/company-profile</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">toastr::messages</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>toastr::messages</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n      \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"25 characters\">&#272;&#227; l&#432;u th&#224;nh c&#244;ng b&#7843;n ghi</span>\"\n      \"<span class=sf-dump-key>options</span>\" => []\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779616327\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://recland.local/employer/change-info-company", "action_name": "employer-change-info-company", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@changeInfoCompany"}, "badge": "302 Found"}}