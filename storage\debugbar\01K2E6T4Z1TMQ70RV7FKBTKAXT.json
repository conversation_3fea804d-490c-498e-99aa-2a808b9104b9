{"__meta": {"id": "01K2E6T4Z1TMQ70RV7FKBTKAXT", "datetime": "2025-08-12 10:57:30", "utime": **********.978002, "method": "GET", "uri": "/employer/company-profile", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "apache2handler"}, "messages": {"count": 32, "messages": [{"message": "[10:57:30] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\BannerService.php on line 19", "message_html": null, "is_string": false, "label": "warning", "time": **********.637581, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.warning: Optional parameter $type declared before required parameter $position is implicitly treated as a required parameter in D:\\Projects\\HRI\\RecLand\\app\\Repositories\\BannerRepository.php on line 53", "message_html": null, "is_string": false, "label": "warning", "time": **********.638097, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-company-profile' limit 1\\n-- \",\n    \"Time:\": 26.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.699647, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\ninsert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http:\\/\\/recland.local\\/employer\\/company-profile', 'http:\\/\\/recland.local\\/employer\\/job\\/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mo<PERSON>\\\\\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\\\\\/537.36 (KHTML, like Gecko) Chrome\\\\\\/********* Safari\\\\\\/537.36\\\"],\\\"accept\\\":[\\\"text\\\\\\/html,application\\\\\\/xhtml+xml,application\\\\\\/xml;q=0.9,image\\\\\\/avif,image\\\\\\/webp,image\\\\\\/apng,*\\\\\\/*;q=0.8,application\\\\\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\\\\\/\\\\\\/recland.local\\\\\\/employer\\\\\\/job\\\\\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754966673$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkRMYXh4L3FzVUJDaWF2cHFMUDl2TUE9PSIsInZhbHVlIjoiN2VKZjhiTUdrWGQ4RGZMdW5kdU1nMDZEbnk5Y1NDQ09OQVFSbWtGNkMvZ1RyK2tsMWZRNmJsSDRvYWNVZlRzdDhoQ1dsR3pTRnJyWVBSakZqeGkxaklsT1d1T0FmeTRTZUV4TjUwd1BzQ2wxNnVTaEtzZldWU1F3bDUxT2dmU2giLCJtYWMiOiIyYWEwNjI2NjUyZTgwN2RiYWYzZTc3NmQ5NTA4OThkN2RjMjhhZDI3NDBmNzJmOGRhNjQ0MzE1N2ZkMmJiZmZkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjJ2WmkxT1lMcUthK1JvbVZ0cmlrSFE9PSIsInZhbHVlIjoiYUdWekxTVnJhR01KaHVCN3FOenB0cEtsYU5rYTdDVWFJcW9yVWN3RFNmRGMwODNYdGp5OFBteVRlL1dmcWN3YWVsMGFTRHcwWk1OdytrSjkzOW1vSTR4N3RqWlgzSW1YWXpIcjVxQ1VtbnFQZzIwUktvSTVQa0xvU1NodUxESWUiLCJtYWMiOiI4YTU5ZTIyYWJkMmJjNTY1MTkxMzIwYmQ4NTE1M2RmZjZhMjU2NTk5MDgyODM4OTQ2N2JhM2RkOTY1YTVhOGRjIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 10:57:30', '2025-08-12 10:57:30')\\n-- \",\n    \"Time:\": 0.85\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.740281, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `users` where `id` = '1723' limit 1\\n-- \",\n    \"Time:\": 0.53\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.745288, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.51\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.75159, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.35\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.753955, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `wallets` where `wallets`.`user_id` = '1723' and `wallets`.`user_id` is not null limit 1\\n-- \",\n    \"Time:\": 4.29\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.760021, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `seos` where `key` = 'employer-company-profile' limit 1\\n-- \",\n    \"Time:\": 0.52\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.763887, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.75\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.7662, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employee_roles` where 0 = 1\\n-- \",\n    \"Time:\": 0.3\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.767633, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `companies` where `companies`.`id` = '84' and `companies`.`id` is not null limit 1\\n-- \",\n    \"Time:\": 0.55\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.769769, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 174", "message_html": null, "is_string": false, "label": "warning", "time": **********.920975, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in D:\\Projects\\HRI\\RecLand\\app\\Models\\Company.php on line 197", "message_html": null, "is_string": false, "label": "warning", "time": **********.921118, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 1.13\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.927192, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.89\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.931739, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.77\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.933886, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.93578, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.937661, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.939548, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.72\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.941453, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `meta_data` where `meta_data`.`object_type` = 'App\\\\Models\\\\User' and `meta_data`.`object_id` = '1723' and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1\\n-- \",\n    \"Time:\": 0.58\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.944919, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.6\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.950589, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\Models\\\\User' and `notifications`.`notifiable_id` = '1723' and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc\\n-- \",\n    \"Time:\": 0.54\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.952425, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.76\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.955274, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.958579, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.83\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.960589, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.8\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.962571, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.74\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.964498, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.96639, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.73\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.968309, "xdebug_link": null, "collector": "log"}, {"message": "[10:57:30] LOG.debug: [SQL EXEC] {\n    \"SQL:\": \"\\nselect * from `employer_types` where `employer_types`.`user_id` in (1723)\\n-- \",\n    \"Time:\": 0.57\n}", "message_html": null, "is_string": false, "label": "debug", "time": **********.970023, "xdebug_link": null, "collector": "log"}]}, "time": {"count": 3, "start": **********.21283, "end": **********.978054, "duration": 0.7652239799499512, "duration_str": "765ms", "measures": [{"label": "Booting", "start": **********.21283, "relative_start": 0, "end": **********.612287, "relative_end": **********.612287, "duration": 0.****************, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.612298, "relative_start": 0.*****************, "end": **********.978057, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.627109, "relative_start": 0.****************, "end": **********.632855, "relative_end": **********.632855, "duration": 0.005745887756347656, "duration_str": "5.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "61MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 16, "nb_templates": 16, "templates": [{"name": "1x frontend.pages.employer.company_profile", "param_count": null, "params": [], "start": **********.782659, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/pages/employer/company_profile.blade.phpfrontend.pages.employer.company_profile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpages%2Femployer%2Fcompany_profile.blade.php&line=1", "ajax": false, "filename": "company_profile.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.pages.employer.company_profile"}, {"name": "1x frontend.layouts.employer.app", "param_count": null, "params": [], "start": **********.923527, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/app.blade.phpfrontend.layouts.employer.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.employer.app"}, {"name": "2x frontend.layouts.user.avatar", "param_count": null, "params": [], "start": **********.92425, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/user/avatar.blade.phpfrontend.layouts.user.avatar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fuser%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.user.avatar"}, {"name": "2x frontend.layouts.employer.menu", "param_count": null, "params": [], "start": **********.924796, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.phpfrontend.layouts.employer.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.layouts.employer.menu"}, {"name": "1x frontend.layouts.modal.employer_confirm", "param_count": null, "params": [], "start": **********.942728, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.phpfrontend.layouts.modal.employer_confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Fmodal%2Femployer_confirm.blade.php&line=1", "ajax": false, "filename": "employer_confirm.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.modal.employer_confirm"}, {"name": "1x frontend.layouts.login.app", "param_count": null, "params": [], "start": **********.946182, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/login/app.blade.phpfrontend.layouts.login.app", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Flogin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.layouts.login.app"}, {"name": "1x frontend.inc_layouts.v2.header_script", "param_count": null, "params": [], "start": **********.947071, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/v2/header_script.blade.phpfrontend.inc_layouts.v2.header_script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fv2%2Fheader_script.blade.php&line=1", "ajax": false, "filename": "header_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.v2.header_script"}, {"name": "1x frontend.inc_layouts.login.header", "param_count": null, "params": [], "start": **********.947656, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.phpfrontend.inc_layouts.login.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.header"}, {"name": "2x frontend.inc_layouts.notification.drop-notification", "param_count": null, "params": [], "start": **********.95348, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/notification/drop-notification.blade.phpfrontend.inc_layouts.notification.drop-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Fnotification%2Fdrop-notification.blade.php&line=1", "ajax": false, "filename": "drop-notification.blade.php", "line": "?"}, "render_count": 2, "name_original": "frontend.inc_layouts.notification.drop-notification"}, {"name": "1x frontend.inc_layouts.login.footer_v2", "param_count": null, "params": [], "start": **********.971374, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/footer_v2.blade.phpfrontend.inc_layouts.login.footer_v2", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Ffooter_v2.blade.php&line=1", "ajax": false, "filename": "footer_v2.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.footer_v2"}, {"name": "1x frontend.inc_layouts.login.modal_report", "param_count": null, "params": [], "start": **********.972357, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/modal_report.blade.phpfrontend.inc_layouts.login.modal_report", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fmodal_report.blade.php&line=1", "ajax": false, "filename": "modal_report.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.inc_layouts.login.modal_report"}, {"name": "1x frontend.partials.bug_report_modal", "param_count": null, "params": [], "start": **********.973199, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/frontend/partials/bug_report_modal.blade.phpfrontend.partials.bug_report_modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Fpartials%2Fbug_report_modal.blade.php&line=1", "ajax": false, "filename": "bug_report_modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.partials.bug_report_modal"}, {"name": "1x admin.inc_layouts.toast.message", "param_count": null, "params": [], "start": **********.974624, "type": "blade", "hash": "bladeD:\\Projects\\HRI\\RecLand\\resources\\views/admin/inc_layouts/toast/message.blade.phpadmin.inc_layouts.toast.message", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Fadmin%2Finc_layouts%2Ftoast%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.inc_layouts.toast.message"}]}, "route": {"uri": "GET employer/company-profile", "middleware": "web, localization, visit-website, check-employer, Closure", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=483\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "as": "employer-company-profile", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=483\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:483-521</a>"}, "queries": {"count": 28, "nb_statements": 28, "nb_visible_statements": 28, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04853, "accumulated_duration_str": "48.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `seos` where `key` = 'employer-company-profile' limit 1", "type": "query", "params": [], "bindings": ["employer-company-profile"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 122}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 929}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 770}], "start": **********.673439, "duration": 0.02655, "duration_str": "26.55ms", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "a2fb194f71247ecbe73294b393cbedbd845542b0d8c7d55f7c5b9cba51e1c1cf"}, "start_percent": 0, "width_percent": 54.708}, {"sql": "insert into `shetabit_visits` (`method`, `request`, `url`, `referer`, `languages`, `useragent`, `headers`, `device`, `platform`, `browser`, `ip`, `visitor_id`, `visitor_type`, `updated_at`, `created_at`) values ('GET', '[]', 'http://recland.local/employer/company-profile', 'http://recland.local/employer/job/create', '[\\\"vi\\\",\\\"en-us\\\",\\\"en\\\"]', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\\\"host\\\":[\\\"recland.local\\\"],\\\"connection\\\":[\\\"keep-alive\\\"],\\\"upgrade-insecure-requests\\\":[\\\"1\\\"],\\\"user-agent\\\":[\\\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\\\"],\\\"accept\\\":[\\\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\\\"],\\\"referer\\\":[\\\"http:\\/\\/recland.local\\/employer\\/job\\/create\\\"],\\\"accept-encoding\\\":[\\\"gzip, deflate\\\"],\\\"accept-language\\\":[\\\"vi,en-US;q=0.9,en;q=0.8\\\"],\\\"cookie\\\":[\\\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754966673$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkRMYXh4L3FzVUJDaWF2cHFMUDl2TUE9PSIsInZhbHVlIjoiN2VKZjhiTUdrWGQ4RGZMdW5kdU1nMDZEbnk5Y1NDQ09OQVFSbWtGNkMvZ1RyK2tsMWZRNmJsSDRvYWNVZlRzdDhoQ1dsR3pTRnJyWVBSakZqeGkxaklsT1d1T0FmeTRTZUV4TjUwd1BzQ2wxNnVTaEtzZldWU1F3bDUxT2dmU2giLCJtYWMiOiIyYWEwNjI2NjUyZTgwN2RiYWYzZTc3NmQ5NTA4OThkN2RjMjhhZDI3NDBmNzJmOGRhNjQ0MzE1N2ZkMmJiZmZkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjJ2WmkxT1lMcUthK1JvbVZ0cmlrSFE9PSIsInZhbHVlIjoiYUdWekxTVnJhR01KaHVCN3FOenB0cEtsYU5rYTdDVWFJcW9yVWN3RFNmRGMwODNYdGp5OFBteVRlL1dmcWN3YWVsMGFTRHcwWk1OdytrSjkzOW1vSTR4N3RqWlgzSW1YWXpIcjVxQ1VtbnFQZzIwUktvSTVQa0xvU1NodUxESWUiLCJtYWMiOiI4YTU5ZTIyYWJkMmJjNTY1MTkxMzIwYmQ4NTE1M2RmZjZhMjU2NTk5MDgyODM4OTQ2N2JhM2RkOTY1YTVhOGRjIiwidGFnIjoiIn0%3D\\\"]}', 'WebKit', 'Windows', 'Chrome', '127.0.0.1', '', '', '2025-08-12 10:57:30', '2025-08-12 10:57:30')", "type": "query", "params": [], "bindings": ["GET", "[]", "http://recland.local/employer/company-profile", "http://recland.local/employer/job/create", "[\"vi\",\"en-us\",\"en\"]", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "{\"host\":[\"recland.local\"],\"connection\":[\"keep-alive\"],\"upgrade-insecure-requests\":[\"1\"],\"user-agent\":[\"Mozilla\\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\\/537.36 (KHTML, like Gecko) Chrome\\/********* Safari\\/537.36\"],\"accept\":[\"text\\/html,application\\/xhtml+xml,application\\/xml;q=0.9,image\\/avif,image\\/webp,image\\/apng,*\\/*;q=0.8,application\\/signed-exchange;v=b3;q=0.7\"],\"referer\":[\"http:\\/\\/recland.local\\/employer\\/job\\/create\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"vi,en-US;q=0.9,en;q=0.8\"],\"cookie\":[\"remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754966673$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkRMYXh4L3FzVUJDaWF2cHFMUDl2TUE9PSIsInZhbHVlIjoiN2VKZjhiTUdrWGQ4RGZMdW5kdU1nMDZEbnk5Y1NDQ09OQVFSbWtGNkMvZ1RyK2tsMWZRNmJsSDRvYWNVZlRzdDhoQ1dsR3pTRnJyWVBSakZqeGkxaklsT1d1T0FmeTRTZUV4TjUwd1BzQ2wxNnVTaEtzZldWU1F3bDUxT2dmU2giLCJtYWMiOiIyYWEwNjI2NjUyZTgwN2RiYWYzZTc3NmQ5NTA4OThkN2RjMjhhZDI3NDBmNzJmOGRhNjQ0MzE1N2ZkMmJiZmZkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjJ2WmkxT1lMcUthK1JvbVZ0cmlrSFE9PSIsInZhbHVlIjoiYUdWekxTVnJhR01KaHVCN3FOenB0cEtsYU5rYTdDVWFJcW9yVWN3RFNmRGMwODNYdGp5OFBteVRlL1dmcWN3YWVsMGFTRHcwWk1OdytrSjkzOW1vSTR4N3RqWlgzSW1YWXpIcjVxQ1VtbnFQZzIwUktvSTVQa0xvU1NodUxESWUiLCJtYWMiOiI4YTU5ZTIyYWJkMmJjNTY1MTkxMzIwYmQ4NTE1M2RmZjZhMjU2NTk5MDgyODM4OTQ2N2JhM2RkOTY1YTVhOGRjIiwidGFnIjoiIn0%3D\"]}", "WebKit", "Windows", "Chrome", "127.0.0.1", null, null, "2025-08-12 10:57:30", "2025-08-12 10:57:30"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, {"index": 22, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 20}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.739506, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:245", "source": {"index": 21, "namespace": null, "name": "vendor/shetabit/visitor/src/Visitor.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\shetabit\\visitor\\src\\Visitor.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FVisitor.php&line=245", "ajax": false, "filename": "Visitor.php", "line": "245"}, "connection": "hri_recland_product", "explain": null, "start_percent": 54.708, "width_percent": 1.751}, {"sql": "select * from `users` where `id` = 1723 limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.744829, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `users` where `id` = ? limit 1", "hash": "b745d78408bc8e8134214d3e160a17ee7da2cf5aa5c603b93fa88548ba5e55b9"}, "start_percent": 56.46, "width_percent": 1.092}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 21, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 23, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.7511501, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 19, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 57.552, "width_percent": 1.051}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 26, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 28, "namespace": "middleware", "name": "localization", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\Localization.php", "line": 24}], "start": **********.753675, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "check-employer:28", "source": {"index": 24, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FMiddleware%2FCheckEmployer.php&line=28", "ajax": false, "filename": "CheckEmployer.php", "line": "28"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 58.603, "width_percent": 0.721}, {"sql": "select * from `wallets` where `wallets`.`user_id` = 1723 and `wallets`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": [1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 162}, {"index": 22, "namespace": "middleware", "name": "check-employer", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\CheckEmployer.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 24, "namespace": "middleware", "name": "visit-website", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Middleware\\VisitWebsiteMiddleware.php", "line": 21}], "start": **********.7559018, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "EmployerController.php:125", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 125}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=125", "ajax": false, "filename": "EmployerController.php", "line": "125"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `wallets` where `wallets`.`user_id` = ? and `wallets`.`user_id` is not null limit 1", "hash": "ef913f8afc9f4d48f551727e02040c91d14047cbc5f6bb1c2d8490bb794fe689"}, "start_percent": 59.324, "width_percent": 8.84}, {"sql": "select * from `seos` where `key` = 'employer-company-profile' limit 1", "type": "query", "params": [], "bindings": ["employer-company-profile"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, {"index": 16, "namespace": null, "name": "app/Services/Frontend/SeoService.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Services\\Frontend\\SeoService.php", "line": 22}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 490}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.763438, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "SeoRepository.php:14", "source": {"index": 15, "namespace": null, "name": "app/Repositories/SeoRepository.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Repositories\\SeoRepository.php", "line": 14}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FRepositories%2FSeoRepository.php&line=14", "ajax": false, "filename": "SeoRepository.php", "line": "14"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `seos` where `key` = ? limit 1", "hash": "a2fb194f71247ecbe73294b393cbedbd845542b0d8c7d55f7c5b9cba51e1c1cf"}, "start_percent": 68.164, "width_percent": 1.072}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 491}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.765518, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "EmployerController.php:491", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 491}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=491", "ajax": false, "filename": "EmployerController.php", "line": "491"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 69.236, "width_percent": 1.545}, {"sql": "select * from `employee_roles` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 491}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.767398, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "EmployerController.php:491", "source": {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 491}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=491", "ajax": false, "filename": "EmployerController.php", "line": "491"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employee_roles` where 0 = 1", "hash": "7abe75af857868e401c008d204701c939df6929543a52ceec69a58c2d821c305"}, "start_percent": 70.781, "width_percent": 0.618}, {"sql": "select * from `companies` where `companies`.`id` = 84 and `companies`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [84], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/EmployerController.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Http\\Controllers\\Frontend\\EmployerController.php", "line": 515}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.76929, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MetableBase.php:181", "source": {"index": 20, "namespace": null, "name": "vendor/zoha/laravel-meta/src/Meta/Traits/MetableBase.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\zoha\\laravel-meta\\src\\Meta\\Traits\\MetableBase.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fzoha%2Flaravel-meta%2Fsrc%2FMeta%2FTraits%2FMetableBase.php&line=181", "ajax": false, "filename": "MetableBase.php", "line": "181"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `companies` where `companies`.`id` = ? and `companies`.`id` is not null limit 1", "hash": "31e318b16eac4c3b62f251a2680062a21213325b8e61a770083fea3d040b5bd3"}, "start_percent": 71.399, "width_percent": 1.133}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.9262278, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 72.532, "width_percent": 2.328}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.930999, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 74.861, "width_percent": 1.834}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.933184, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 76.695, "width_percent": 1.587}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.935106, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 78.281, "width_percent": 1.525}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.936996, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 79.806, "width_percent": 1.504}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.938884, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 81.311, "width_percent": 1.504}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.940798, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 82.815, "width_percent": 1.484}, {"sql": "select * from `meta_data` where `meta_data`.`object_type` = 'App\\Models\\User' and `meta_data`.`object_id` = 1723 and `meta_data`.`object_id` is not null and `key` = 'employer_confirmed_at' limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723, "employer_confirmed_at"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, {"index": 19, "namespace": "view", "name": "frontend.layouts.modal.employer_confirm", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/modal/employer_confirm.blade.php", "line": 5}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}], "start": **********.944409, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "User.php:197", "source": {"index": 18, "namespace": null, "name": "app/Models/User.php", "file": "D:\\Projects\\HRI\\RecLand\\app\\Models\\User.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=197", "ajax": false, "filename": "User.php", "line": "197"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `meta_data` where `meta_data`.`object_type` = ? and `meta_data`.`object_id` = ? and `meta_data`.`object_id` is not null and `key` = ? limit 1", "hash": "1e919bf895733b158fa816fe17ad059167697afb952d846aaeabf40228f5fa12"}, "start_percent": 84.298, "width_percent": 1.195}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 1723 and `notifications`.`notifiable_id` is not null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.950058, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:98", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 98}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=98", "ajax": false, "filename": "header.blade.php", "line": "98"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null order by `created_at` desc", "hash": "2c9622ba988b61fa8c1f7fbcd422804726d734b2ef6b91781d7446b85a6e43d8"}, "start_percent": 85.494, "width_percent": 1.236}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\Models\\User' and `notifications`.`notifiable_id` = 1723 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\Models\\User", 1723], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.951955, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:99", "source": {"index": 20, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=99", "ajax": false, "filename": "header.blade.php", "line": "99"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `notifications` where `notifications`.`notifiable_type` = ? and `notifications`.`notifiable_id` = ? and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "hash": "f6ba9bdd77fc193fe60a0e5b92c8d6d03185ee2af0d5baf1deec78db97c629a0"}, "start_percent": 86.73, "width_percent": 1.113}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.954583, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "frontend.inc_layouts.login.header:243", "source": {"index": 19, "namespace": "view", "name": "frontend.inc_layouts.login.header", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/inc_layouts/login/header.blade.php", "line": 243}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Finc_layouts%2Flogin%2Fheader.blade.php&line=243", "ajax": false, "filename": "header.blade.php", "line": "243"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 87.843, "width_percent": 1.566}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.957907, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 89.409, "width_percent": 1.525}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.9598281, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 90.933, "width_percent": 1.71}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.9618392, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 92.644, "width_percent": 1.648}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.9638278, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 94.292, "width_percent": 1.525}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.965744, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 95.817, "width_percent": 1.504}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.967646, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 97.321, "width_percent": 1.504}, {"sql": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 110}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 70}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "D:\\Projects\\HRI\\RecLand\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "start": **********.9695199, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "frontend.layouts.employer.menu:70", "source": {"index": 19, "namespace": "view", "name": "frontend.layouts.employer.menu", "file": "D:\\Projects\\HRI\\RecLand\\resources\\views/frontend/layouts/employer/menu.blade.php", "line": 70}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fresources%2Fviews%2Ffrontend%2Flayouts%2Femployer%2Fmenu.blade.php&line=70", "ajax": false, "filename": "menu.blade.php", "line": "70"}, "connection": "hri_recland_product", "explain": {"url": "http://recland.local/_debugbar/queries/explain", "driver": "mysql", "connection": "mysql", "query": "select * from `employer_types` where `employer_types`.`user_id` in (1723)", "hash": "5ebfaff83a8804adee733c810edacd9814c80e3d5c761c394792d47bc7228d7d"}, "start_percent": 98.825, "width_percent": 1.175}]}, "models": {"data": {"App\\Models\\EmployerType": {"retrieved": 17, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FEmployerType.php&line=1", "ajax": false, "filename": "EmployerType.php", "line": "?"}}, "App\\Models\\Seo": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FSeo.php&line=1", "ajax": false, "filename": "Seo.php", "line": "?"}}, "Shetabit\\Visitor\\Models\\Visit": {"created": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fvendor%2Fshetabit%2Fvisitor%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Wallet": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FWallet.php&line=1", "ajax": false, "filename": "Wallet.php", "line": "?"}}, "App\\Models\\Company": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\MetaData": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FModels%2FMetaData.php&line=1", "ajax": false, "filename": "MetaData.php", "line": "?"}}}, "count": 24, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 23, "created": 1}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna", "_previous": "array:1 [\n  \"url\" => \"http://recland.local/employer/job/create\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1723", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://recland.local/employer/company-profile", "action_name": "employer-company-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile", "uri": "GET employer/company-profile", "controller": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=483\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Frontend", "prefix": "/employer", "file": "<a href=\"phpstorm://open?file=D%3A%2FProjects%2FHRI%2FRecLand%2Fapp%2FHttp%2FControllers%2FFrontend%2FEmployerController.php&line=483\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/EmployerController.php:483-521</a>", "middleware": "web, localization, visit-website, check-employer", "duration": "769ms", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1167239530 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1167239530\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1198667819 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1198667819\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1285154959 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">recland.local</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">vi,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1352 characters\">remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IndxdGtmVkY4UFkxbjE2K2JqbCs5WGc9PSIsInZhbHVlIjoiNVBrSzN0UmhzTzdaTWs3WlRRekZPTHVaTkZjaHlWa25PRWF6QnQ4UjlhaTRDUXh1dVRPMVNKZW5CN1ZyMFQ5Z1pSRHhBV1I3VTVnRDNpYk1VSzdoZFlVQXBTSW85N1NUTzNyVGYycThnVmtTbWJmUGZISlBvdWI1OGdmSGtPL2R6Smt3RTQ2VHRtMzgzcHpVRVRjcENEcitXMEpkMjdrdTB5enN2NDNZTVB3cmxBZ0VNbEJlZkZLSnByWmRFVnR3b1NsMVFUNGNhbUV0YjJCTTNzNm80andMdG53SG93ZEpKeVNkbk9mTUN0ND0iLCJtYWMiOiI1YmYyYjE3ZDYyZjQ3ZjJhNzQ2YjliMWU3MTA5MmIxMWU0NzQ2ZjI3M2RlOTZmODVmZjc5ZDZhMTJlNzk3NGU1IiwidGFnIjoiIn0%3D; _ga=GA1.1.1592575569.1754907683; _ga_G4VPZYJ5H7=GS2.1.s1754962034$o2$g1$t1754966673$j60$l0$h0; XSRF-TOKEN=eyJpdiI6IkRMYXh4L3FzVUJDaWF2cHFMUDl2TUE9PSIsInZhbHVlIjoiN2VKZjhiTUdrWGQ4RGZMdW5kdU1nMDZEbnk5Y1NDQ09OQVFSbWtGNkMvZ1RyK2tsMWZRNmJsSDRvYWNVZlRzdDhoQ1dsR3pTRnJyWVBSakZqeGkxaklsT1d1T0FmeTRTZUV4TjUwd1BzQ2wxNnVTaEtzZldWU1F3bDUxT2dmU2giLCJtYWMiOiIyYWEwNjI2NjUyZTgwN2RiYWYzZTc3NmQ5NTA4OThkN2RjMjhhZDI3NDBmNzJmOGRhNjQ0MzE1N2ZkMmJiZmZkIiwidGFnIjoiIn0%3D; recland_session=eyJpdiI6IjJ2WmkxT1lMcUthK1JvbVZ0cmlrSFE9PSIsInZhbHVlIjoiYUdWekxTVnJhR01KaHVCN3FOenB0cEtsYU5rYTdDVWFJcW9yVWN3RFNmRGMwODNYdGp5OFBteVRlL1dmcWN3YWVsMGFTRHcwWk1OdytrSjkzOW1vSTR4N3RqWlgzSW1YWXpIcjVxQ1VtbnFQZzIwUktvSTVQa0xvU1NodUxESWUiLCJtYWMiOiI4YTU5ZTIyYWJkMmJjNTY1MTkxMzIwYmQ4NTE1M2RmZjZhMjU2NTk5MDgyODM4OTQ2N2JhM2RkOTY1YTVhOGRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285154959\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1162902734 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|cm8f4SgFu8wrAd7wPR9b0gsQwQ8YYGlGSWS8Y9hgKQXIsj7qT8VG3KwC3dMx|$2y$10$iTSniY9ReMSnjm6GaX0bRuGWPNsyk9pQat6r8dKHdyoQSgwMkVojS</span>\"\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_G4VPZYJ5H7</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>recland_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">l14rqZszo9qIJxuJ9MGKepBdp78uKu02fXl3sfMX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162902734\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1469627061 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 12 Aug 2025 03:57:30 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469627061\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14442452 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">hm4rSIgStnxsyBGcjYYzcvc2mPcOjpiTKJlz6Xna</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://recland.local/employer/job/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>login_client_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1723</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14442452\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://recland.local/employer/company-profile", "action_name": "employer-company-profile", "controller_action": "App\\Http\\Controllers\\Frontend\\EmployerController@companyProfile"}, "badge": null}}